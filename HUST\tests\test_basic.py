"""
基础功能测试
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_main_logger
from core.database.connection import db_manager
from core.excel.reader import ExcelReader
from processors.backup_manager import BackupManager
from config.settings import FILES
from config.mapping import validate_mapping_config

def test_database_connection():
    """测试数据库连接"""
    logger = get_main_logger()
    logger.info("测试数据库连接...")
    
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
        
        logger.info("✓ 数据库连接测试通过")
        return True
    except Exception as e:
        logger.error(f"✗ 数据库连接测试失败: {str(e)}")
        return False

def test_excel_reader():
    """测试Excel读取功能"""
    logger = get_main_logger()
    logger.info("测试Excel读取功能...")
    
    try:
        source_file = project_root / FILES['source_file']
        
        if not source_file.exists():
            logger.warning(f"源文件不存在，跳过Excel读取测试: {source_file}")
            return True
        
        reader = ExcelReader(str(source_file))
        
        # 测试加载工作簿
        if not reader.load_workbook():
            logger.error("✗ 加载Excel工作簿失败")
            return False
        
        # 测试获取工作表信息
        sheet_info = reader.get_sheet_info()
        if not sheet_info:
            logger.error("✗ 获取工作表信息失败")
            return False
        
        logger.info(f"✓ Excel读取测试通过，找到 {len(sheet_info)} 个工作表")
        
        # 显示工作表信息
        for sheet_name, info in sheet_info.items():
            logger.info(f"  - {sheet_name}: {info.get('row_count', 0)} 行, {info.get('column_count', 0)} 列")
        
        reader.close()
        return True
        
    except Exception as e:
        logger.error(f"✗ Excel读取测试失败: {str(e)}")
        return False

def test_backup_manager():
    """测试备份管理功能"""
    logger = get_main_logger()
    logger.info("测试备份管理功能...")
    
    try:
        backup_manager = BackupManager()
        
        # 测试列出备份文件
        backups = backup_manager.list_backups()
        logger.info(f"✓ 备份管理测试通过，找到 {len(backups)} 个备份文件")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 备份管理测试失败: {str(e)}")
        return False

def test_mapping_config():
    """测试映射配置"""
    logger = get_main_logger()
    logger.info("测试映射配置...")
    
    try:
        validation_result = validate_mapping_config()
        
        if validation_result['valid']:
            logger.info("✓ 映射配置验证通过")
        else:
            logger.warning("⚠ 映射配置有问题:")
            for error in validation_result['errors']:
                logger.warning(f"  错误: {error}")
            for warning in validation_result['warnings']:
                logger.warning(f"  警告: {warning}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 映射配置测试失败: {str(e)}")
        return False

def test_file_structure():
    """测试文件结构"""
    logger = get_main_logger()
    logger.info("测试文件结构...")
    
    try:
        # 检查关键目录
        required_dirs = [
            project_root / 'core',
            project_root / 'processors', 
            project_root / 'config',
            project_root / 'utils',
            project_root / 'backups'
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                logger.error(f"✗ 必需目录不存在: {dir_path}")
                return False
        
        # 检查关键文件
        required_files = [
            project_root / 'config' / 'settings.py',
            project_root / 'config' / 'mapping.py',
            project_root / 'utils' / 'logger.py',
            project_root / 'core' / 'database' / 'connection.py'
        ]
        
        for file_path in required_files:
            if not file_path.exists():
                logger.error(f"✗ 必需文件不存在: {file_path}")
                return False
        
        logger.info("✓ 文件结构测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 文件结构测试失败: {str(e)}")
        return False

def run_basic_tests():
    """运行所有基础测试"""
    logger = get_main_logger()
    logger.info("开始运行基础测试...")
    
    tests = [
        ("文件结构", test_file_structure),
        ("数据库连接", test_database_connection),
        ("Excel读取", test_excel_reader),
        ("备份管理", test_backup_manager),
        ("映射配置", test_mapping_config)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"测试 {test_name} 失败")
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {str(e)}")
    
    logger.info(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有基础测试通过！")
    else:
        logger.warning(f"⚠ {total - passed} 个测试失败")
    
    return passed == total

if __name__ == "__main__":
    # 初始化日志
    from utils.logger import init_main_logger
    init_main_logger()
    
    # 运行测试
    run_basic_tests()
