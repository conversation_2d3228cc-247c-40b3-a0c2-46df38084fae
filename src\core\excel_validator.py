"""
Excel文件验证器模块
"""
import pandas as pd
from openpyxl import load_workbook
from ..utils.logger import setup_logger
from ..utils.config import EXCEL_CONFIG, COLUMN_NAMES
import tkinter as tk
from tkinter import messagebox, scrolledtext

logger = setup_logger(__name__)

class ExcelValidator:
    """Excel文件验证器类"""
    
    def __init__(self, file_path: str):
        """
        初始化验证器
        
        Args:
            file_path (str): Excel文件路径
        """
        self.file_path = file_path
        self.wb = load_workbook(file_path)
        self.df_machines = None  # 机组sheet的数据
        self.df_hydro = None     # 水电三段式出力sheet的数据
        self.df_wind = None      # 风电场sheet的数据
        self.df_solar = None     # 光伏电站sheet的数据
        self.df_offer = None     # 机组报价sheet的数据
        self.df_quotes = None    # 机组报价sheet的数据
        self.df_status = None    # 机组指定状态sheet的数据
        self.df_output = None    # 机组指定出力sheet的数据
        
    def load_data(self):
        """加载所有需要的数据"""
        try:
            # 加载机组sheet数据
            self.df_machines = pd.read_excel(
                self.file_path,
                sheet_name=EXCEL_CONFIG['output_sheet'],
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载水电三段式出力sheet数据
            self.df_hydro = pd.read_excel(
                self.file_path,
                sheet_name='水电三段式出力',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载风电场sheet数据
            self.df_wind = pd.read_excel(
                self.file_path,
                sheet_name='风电场',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载光伏电站sheet数据
            self.df_solar = pd.read_excel(
                self.file_path,
                sheet_name='光伏电站',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载机组报价sheet数据
            self.df_offer = pd.read_excel(
                self.file_path,
                sheet_name='机组报价',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载机组报价sheet数据
            self.df_quotes = pd.read_excel(
                self.file_path,
                sheet_name='机组报价',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载机组指定状态sheet数据
            self.df_status = pd.read_excel(
                self.file_path,
                sheet_name='机组指定状态',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            # 加载机组指定出力sheet数据
            self.df_output = pd.read_excel(
                self.file_path,
                sheet_name='机组指定出力',
                engine=EXCEL_CONFIG['engine'],
                na_filter=EXCEL_CONFIG['na_filter']
            )
            
            logger.info("数据加载完成")
            return True
            
        except Exception as e:
            logger.error(f"加载数据时出错: {str(e)}")
            return False
    
    def remove_duplicate_machines(self):
        """
        剔除机组名称重复项，保留第一次出现的记录
        
        Returns:
            bool: 是否成功
        """
        try:
            if self.df_machines is None:
                logger.error("请先加载数据")
                return False
                
            # 获取重复的机组名称
            duplicates = self.df_machines[self.df_machines.duplicated(subset=[COLUMN_NAMES['machine_name']], keep=False)]
            
            if not duplicates.empty:
                logger.warning("发现重复的机组名称：")
                for _, row in duplicates.iterrows():
                    logger.warning(f"- {row[COLUMN_NAMES['machine_name']]}")
                
                # 获取要保留的记录（第一次出现的）
                keep_records = self.df_machines.drop_duplicates(subset=[COLUMN_NAMES['machine_name']], keep='first')
                
                # 获取要清空的行（重复记录中除了第一次出现的）
                rows_to_clear = duplicates[~duplicates.index.isin(keep_records.index)].index.tolist()
                
                if not rows_to_clear:
                    logger.warning("未找到要清空的重复机组记录")
                    return False
                
                # 清空要删除的行的内容（保留行）
                ws = self.wb[EXCEL_CONFIG['output_sheet']]
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None
                
                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行重复的机组记录")
            
            return True
            
        except Exception as e:
            logger.error(f"处理重复机组时出错: {str(e)}")
            return False
    
    def check_hydro_plants(self):
        """
        检查水电三段式出力sheet中的水电厂名称是否都在机组sheet中有出现，
        并清空未找到的水电厂对应的行
        
        Returns:
            bool: 是否所有水电厂都存在于机组sheet中
        """
        try:
            if self.df_machines is None or self.df_hydro is None:
                logger.error("请先加载数据")
                return False
            
            # 调试信息：检查机组sheet的列名
            logger.info("\n机组sheet的列名：")
            for col in self.df_machines.columns:
                logger.info(f"- {col}")
            
            # 调试信息：检查机组类型列的值
            logger.info("\n机组类型列的唯一值：")
            logger.info(self.df_machines[COLUMN_NAMES['machine_type_code']].unique())
            
            # 调试信息：检查水电厂列的值
            logger.info("\n水电厂列的前几行数据：")
            logger.info(self.df_machines[COLUMN_NAMES['power_plant']].head())
            
            # 获取机组sheet中的水电厂名称
            hydro_plants = set(self.df_machines[COLUMN_NAMES['power_plant']])
            
            # 获取水电三段式出力sheet中的水电厂名称
            hydro_plants_in_sheet = set(self.df_hydro['水电厂名称'])
            
            # 输出两个sheet中的水电厂名称供比对
            logger.info("\n机组sheet中的水电厂名称：")
            for plant in sorted(hydro_plants):
                logger.info(f"- {plant}")
            
            logger.info("\n水电三段式出力sheet中的水电厂名称：")
            for plant in sorted(hydro_plants_in_sheet):
                logger.info(f"- {plant}")
            
            # 检查缺失的水电厂
            missing_plants = hydro_plants_in_sheet - hydro_plants
            
            if missing_plants:
                logger.warning("\n以下水电厂在水电三段式出力sheet中存在，但在机组sheet中未找到：")
                for plant in sorted(missing_plants):
                    logger.warning(f"- {plant}")
                
                # 询问是否继续清空
                response = input("\n是否要清空这些未找到的水电厂对应的行？(y/n): ")
                if response.lower() != 'y':
                    logger.info("已取消清空操作")
                    return False
                
                # 找到要清空的行
                rows_to_clear = self.df_hydro[self.df_hydro['水电厂名称'].isin(missing_plants)].index.tolist()
                
                if not rows_to_clear:
                    logger.warning("未找到要清空的水电厂记录")
                    return False
                
                # 清空要删除的行的内容（保留行）
                ws = self.wb['水电三段式出力']
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None
                
                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行未找到的水电厂记录")
                return False
            
            logger.info("水电厂检查完成，所有水电厂都存在于机组sheet中")
            return True
            
        except Exception as e:
            logger.error(f"检查水电厂时出错: {str(e)}")
            return False
    
    def check_wind_farm_names(self):
        """
        检查风电场sheet中的风电场名称是否都在机组sheet中有出现，并清空未找到的风电场对应的行
        """
        try:
            if self.df_machines is None or self.df_wind is None:
                logger.error("请先加载数据")
                return False

            # 获取机组sheet中的机组名称
            machine_names = set(self.df_machines[COLUMN_NAMES['machine_name']])

            # 获取风电场sheet中的风电场名称
            wind_farm_col = '注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）'
            wind_farm_names_in_sheet = set(self.df_wind[wind_farm_col])

            # 输出两个sheet中的风电场名称供比对
            logger.info("\n机组sheet中的机组名称：")
            for name in sorted(machine_names):
                logger.info(f"- {name}")

            logger.info("\n风电场sheet中的风电场名称：")
            for name in sorted(wind_farm_names_in_sheet):
                logger.info(f"- {name}")

            # 检查缺失的风电场名称
            missing_names = wind_farm_names_in_sheet - machine_names

            if missing_names:
                logger.warning("\n以下风电场名称在风电场sheet中存在，但在机组sheet中未找到：")
                for name in sorted(missing_names):
                    logger.warning(f"- {name}")

                # 询问是否继续清空
                response = input("\n是否要清空这些未找到的风电场对应的行？(y/n): ")
                if response.lower() != 'y':
                    logger.info("已取消清空操作")
                    return False

                # 找到要清空的行
                rows_to_clear = self.df_wind[self.df_wind[wind_farm_col].isin(missing_names)].index.tolist()

                if not rows_to_clear:
                    logger.warning("未找到要清空的风电场记录")
                    return False

                # 清空要删除的行的内容（保留行）
                ws = self.wb['风电场']
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None

                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行未找到的风电场记录")
                return False

            logger.info("风电场检查完成，所有风电场都存在于机组sheet中")
            return True

        except Exception as e:
            logger.error(f"检查风电场时出错: {str(e)}")
            return False
    
    def check_solar_plants(self):
        """
        检查光伏电站sheet中的光伏电站名称是否都在机组sheet中有出现，
        并清空未找到的光伏电站对应的行
        
        Returns:
            bool: 是否所有光伏电站都存在于机组sheet中
        """
        try:
            if self.df_machines is None or self.df_solar is None:
                logger.error("请先加载数据")
                return False
            
            # 获取机组sheet中的光伏电站名称
            solar_plants = set(self.df_machines[self.df_machines[COLUMN_NAMES['machine_type_code']] == '6'][COLUMN_NAMES['power_plant']])
            
            # 获取光伏电站sheet中的光伏电站名称
            solar_plants_in_sheet = set(self.df_solar['注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）'])
            
            # 输出两个sheet中的光伏电站名称供比对
            logger.info("\n机组sheet中的光伏电站名称：")
            for plant in sorted(solar_plants):
                logger.info(f"- {plant}")
            
            logger.info("\n光伏电站sheet中的光伏电站名称：")
            for plant in sorted(solar_plants_in_sheet):
                logger.info(f"- {plant}")
            
            # 检查缺失的光伏电站
            missing_plants = solar_plants_in_sheet - solar_plants
            
            if missing_plants:
                logger.warning("\n以下光伏电站在光伏电站sheet中存在，但在机组sheet中未找到：")
                for plant in sorted(missing_plants):
                    logger.warning(f"- {plant}")
                
                # 询问是否继续清空
                response = input("\n是否要清空这些未找到的光伏电站对应的行？(y/n): ")
                if response.lower() != 'y':
                    logger.info("已取消清空操作")
                    return False
                
                # 找到要清空的行
                rows_to_clear = self.df_solar[self.df_solar['注：该表格中数据仅用于系统运行模拟[0]光伏电站名称（注：该表中的光伏电站名称需与机组表中的一致）'].isin(missing_plants)].index.tolist()
                
                if not rows_to_clear:
                    logger.warning("未找到要清空的光伏电站记录")
                    return False
                
                # 清空要删除的行的内容（保留行）
                ws = self.wb['光伏电站']
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None
                
                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行未找到的光伏电站记录")
                return False
            
            logger.info("光伏电站检查完成，所有光伏电站都存在于机组sheet中")
            return True
            
        except Exception as e:
            logger.error(f"检查光伏电站时出错: {str(e)}")
            return False
    
    def check_offer_machines(self):
        """
        检查机组报价sheet中的机组名称是否都在机组sheet中有出现，
        并清空未找到的机组名称对应的行
        
        Returns:
            bool: 是否所有机组报价都存在于机组sheet中
        """
        try:
            if self.df_machines is None or self.df_offer is None:
                logger.error("请先加载数据")
                return False
            
            # 获取机组sheet中的机组名称
            machine_names = set(self.df_machines[COLUMN_NAMES['machine_name']])
            
            # 获取机组报价sheet中的机组名称
            offer_names_in_sheet = set(self.df_offer['机组名称'])
            
            # 输出两个sheet中的机组名称供比对
            logger.info("\n机组sheet中的机组名称：")
            for name in sorted(machine_names):
                logger.info(f"- {name}")
            
            logger.info("\n机组报价sheet中的机组名称：")
            for name in sorted(offer_names_in_sheet):
                logger.info(f"- {name}")
            
            # 检查缺失的机组名称
            missing_names = offer_names_in_sheet - machine_names
            
            if missing_names:
                logger.warning("\n以下机组名称在机组报价sheet中存在，但在机组sheet中未找到：")
                for name in sorted(missing_names):
                    logger.warning(f"- {name}")
                
                # 询问是否继续清空
                response = input("\n是否要清空这些未找到的机组名称对应的行？(y/n): ")
                if response.lower() != 'y':
                    logger.info("已取消清空操作")
                    return False
                
                # 找到要清空的行
                rows_to_clear = self.df_offer[self.df_offer['机组名称'].isin(missing_names)].index.tolist()
                
                if not rows_to_clear:
                    logger.warning("未找到要清空的机组报价记录")
                    return False
                
                # 清空要删除的行的内容（保留行）
                ws = self.wb['机组报价']
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None
                
                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行未找到的机组报价记录")
                return False
            
            logger.info("机组报价检查完成，所有机组报价都存在于机组sheet中")
            return True
            
        except Exception as e:
            logger.error(f"检查机组报价时出错: {str(e)}")
            return False
    
    def add_missing_wind_farms(self):
        """
        检查机组sheet中类型为11（风电）的机组名称是否都在风电场sheet有出现，
        不在的话，添加到风电场sheet的A列，B至G列依次为默认风区 3.5 12 25 0.98 0.95 0.15
        """
        try:
            if self.df_machines is None or self.df_wind is None:
                logger.error("请先加载数据")
                return False

            wind_type_col = COLUMN_NAMES['machine_type_code']
            machine_name_col = COLUMN_NAMES['machine_name']
            wind_farm_col = '注：该表格中数据仅用于系统运行模拟[0]风电场名称（注：该表中的风电场名称需与机组表中的一致）'

            logger.info(f"机组sheet所有列名: {self.df_machines.columns.tolist()}")
            logger.info(f"wind_type_col: {wind_type_col}, machine_name_col: {machine_name_col}")
            logger.info(f"机组类型列唯一值: {self.df_machines[wind_type_col].unique()}")

            wind_machines = set(self.df_machines[self.df_machines[wind_type_col] == 11][machine_name_col])
            logger.info("机组sheet中类型为11的机组名称：")
            for name in sorted(wind_machines):
                logger.info(f"- {name}")
            wind_farms_in_sheet = set(self.df_wind[wind_farm_col])

            missing_names = wind_machines - wind_farms_in_sheet

            if not missing_names:
                logger.info("所有风电机组都已在风电场sheet中，无需添加。")
                return True

            logger.info(f"需添加到风电场sheet的机组名称：{missing_names}")

            ws = self.wb['风电场']
            start_row = ws.max_row + 1

            for idx, name in enumerate(sorted(missing_names)):
                row = start_row + idx
                ws.cell(row=row, column=1, value=name)
                ws.cell(row=row, column=2, value="默认风区")
                ws.cell(row=row, column=3, value=3.5)
                ws.cell(row=row, column=4, value=12)
                ws.cell(row=row, column=5, value=25)
                ws.cell(row=row, column=6, value=0.98)
                ws.cell(row=row, column=7, value=0.95)
                ws.cell(row=row, column=8, value=0.15)

            self.wb.save(self.file_path)
            logger.info(f"已添加{len(missing_names)}个风电机组到风电场sheet。")
            return True

        except Exception as e:
            logger.error(f"添加风电场时出错: {str(e)}")
            return False
    
    def add_missing_machine_quotes(self):
        """
        检查机组sheet中的机组名称是否都在机组报价sheet中，
        不在的话，添加到机组报价sheet的A列，B至Z列依次默认为20500101 0.5 0.5 ... 0.5
        """
        try:
            if self.df_machines is None or self.df_quotes is None:
                logger.error("请先加载数据")
                return False

            machine_name_col = COLUMN_NAMES['machine_name']
            
            # 获取机组sheet中的所有机组名称
            all_machines = set(self.df_machines[machine_name_col])
            
            # 获取机组报价sheet中已有的机组名称（第一列）
            existing_quotes = set(self.df_quotes.iloc[:, 0])

            # 找出缺失的机组名称
            missing_names = all_machines - existing_quotes

            if not missing_names:
                logger.info("所有机组都已在机组报价sheet中，无需添加。")
                return True

            logger.info(f"需添加到机组报价sheet的机组名称：{missing_names}")

            # 获取机组报价sheet
            ws = self.wb['机组报价']
            start_row = ws.max_row + 1

            # 默认值列表
            default_values = [20500101] + [0.5] * 24

            # 添加缺失的机组
            for idx, name in enumerate(sorted(missing_names)):
                row = start_row + idx
                # 添加机组名称
                ws.cell(row=row, column=1, value=name)
                # 添加默认值
                for col, value in enumerate(default_values, start=2):
                    ws.cell(row=row, column=col, value=value)

            self.wb.save(self.file_path)
            logger.info(f"已添加{len(missing_names)}个机组到机组报价sheet。")
            return True

        except Exception as e:
            logger.error(f"添加机组报价时出错: {str(e)}")
            return False
    
    def check_machine_status(self):
        """
        检查机组指定状态sheet中的首列的内容是否都在机组sheet中有出现
        如果没有，询问用户是否删除
        """
        try:
            if self.df_machines is None or self.df_status is None:
                logger.error("请先加载数据")
                return False

            machine_name_col = COLUMN_NAMES['machine_name']
            
            # 获取机组sheet中的所有机组名称
            all_machines = set(self.df_machines[machine_name_col])
            
            # 获取机组指定状态sheet中首列的所有机组名称
            status_machines = set(self.df_status.iloc[:, 0])

            # 找出在机组指定状态sheet中存在但在机组sheet中不存在的机组名称
            invalid_names = status_machines - all_machines

            if not invalid_names:
                logger.info("机组指定状态sheet中的所有机组都在机组sheet中存在。")
                return True

            logger.warning(f"机组指定状态sheet中存在以下机组在机组sheet中未找到：{invalid_names}")
            
            # 使用messagebox询问用户
            response = messagebox.askyesno(
                "确认",
                f"发现{len(invalid_names)}个无效机组，是否删除？"
            )
            
            if response:
                # 找到要清空的行
                rows_to_clear = self.df_status[self.df_status.iloc[:, 0].isin(invalid_names)].index.tolist()
                
                if not rows_to_clear:
                    logger.warning("未找到要清空的机组指定状态记录")
                    return False
                
                # 清空要删除的行的内容（保留行）
                ws = self.wb['机组指定状态']
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None
                
                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行无效的机组指定状态记录")
                return True
            else:
                logger.info("用户选择不删除无效机组。")
                return True

        except Exception as e:
            logger.error(f"检查机组指定状态时出错: {str(e)}")
            return False
    
    def check_machine_output(self):
        """
        检查机组指定出力sheet中的首列的内容是否都在机组sheet中有出现
        如果没有，询问用户是否删除
        """
        try:
            if self.df_machines is None or self.df_output is None:
                logger.error("请先加载数据")
                return False

            machine_name_col = COLUMN_NAMES['machine_name']
            
            # 获取机组sheet中的所有机组名称
            all_machines = set(self.df_machines[machine_name_col])
            
            # 获取机组指定出力sheet中首列的所有机组名称
            output_machines = set(self.df_output.iloc[:, 0])

            # 找出在机组指定出力sheet中存在但在机组sheet中不存在的机组名称
            invalid_names = output_machines - all_machines

            if not invalid_names:
                logger.info("机组指定出力sheet中的所有机组都在机组sheet中存在。")
                return True

            logger.warning(f"机组指定出力sheet中存在以下机组在机组sheet中未找到：{invalid_names}")
            
            # 使用messagebox询问用户
            response = messagebox.askyesno(
                "确认",
                f"发现{len(invalid_names)}个无效机组，是否删除？"
            )
            
            if response:
                # 找到要清空的行
                rows_to_clear = self.df_output[self.df_output.iloc[:, 0].isin(invalid_names)].index.tolist()
                
                if not rows_to_clear:
                    logger.warning("未找到要清空的机组指定出力记录")
                    return False
                
                # 清空要删除的行的内容（保留行）
                ws = self.wb['机组指定出力']
                for row in rows_to_clear:
                    row_num = row + 2  # Excel行号从2开始
                    for col in range(1, ws.max_column + 1):
                        ws.cell(row=row_num, column=col).value = None
                
                self.wb.save(self.file_path)
                logger.info(f"已清空{len(rows_to_clear)}行无效的机组指定出力记录")
                return True
            else:
                logger.info("用户选择不删除无效机组。")
                return True

        except Exception as e:
            logger.error(f"检查机组指定出力时出错: {str(e)}")
            return False 