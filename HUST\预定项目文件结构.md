HUST/
├── core/                    # 核心层
│   ├── database/           # 数据库操作
│   │   ├── __init__.py
│   │   ├── connection.py   # 数据库连接管理
│   │   ├── models.py       # 数据模型定义
│   │   └── operations.py   # 数据库操作
│   ├── excel/              # Excel操作
│   │   ├── __init__.py
│   │   ├── reader.py       # Excel读取
│   │   ├── writer.py       # Excel写入
│   │   └── validator.py    # Excel验证
│   └── converter/          # 转换引擎
│       ├── __init__.py
│       ├── base.py         # 基础转换类
│       └── rules.py        # 转换规则定义
├── processors/             # 处理器层
│   ├── __init__.py
│   ├── data_loader.py      # 数据加载器
│   ├── data_transformer.py # 数据转换器
│   ├── data_validator.py   # 数据验证器
│   └── backup_manager.py   # 备份管理器
├── config/                 # 配置层
│   ├── __init__.py
│   ├── settings.py         # 系统配置
│   ├── mapping.py          # 字段映射配置
│   └── rules.py            # 业务规则配置
├── utils/                  # 工具层
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   ├── helpers.py          # 辅助函数
│   └── exceptions.py       # 自定义异常
├── gui/                    # 界面层
│   ├── __init__.py
│   ├── main_window.py      # 主界面
│   └── dialogs.py          # 对话框
└── tests/                  # 测试层
    ├── __init__.py
    ├── test_core.py
    ├── test_processors.py
    └── test_integration.py